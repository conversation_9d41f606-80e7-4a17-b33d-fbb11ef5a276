plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}


def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

/*
def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}
*/

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('keystore.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
} else {
    throw new GradleException("keystore.properties file not found. Create the file in the project root directory.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

/*
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
*/

android {
    namespace "com.cubeonebiz.gate.flutter_onegate"
    compileSdk 35
    ndkVersion "29.0.13113456"  // Updated to match locally installed NDK version
//    flutter.ndkVersion


//    externalNativeBuild {
//        cmake {
//            version "3.31.5"  // Match the required version from your error
//            path "CMakeLists.txt"  // Path to your CMake file if using native code
//        }
//    }

    defaultConfig {
        applicationId "com.cubeonebiz.gate.flutter_onegate"
        minSdkVersion 23
        //noinspection OldTargetApi
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        manifestPlaceholders = [
                appAuthRedirectScheme: 'com.cubeonebiz.gate',
                applicationName      : 'io.flutter.app.FlutterApplication' // Default Flutter Application class
        ]
    }

    signingConfigs {
        release {
            storeFile file(keystoreProperties['storeFile'])
            keyAlias keystoreProperties['alias_name']
            keyPassword keystoreProperties['keyPassword']
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release

            shrinkResources = true
            minifyEnabled true
            proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                    "proguard-rules.pro"
            )
        }
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
}

flutter {
    source '../..'
}